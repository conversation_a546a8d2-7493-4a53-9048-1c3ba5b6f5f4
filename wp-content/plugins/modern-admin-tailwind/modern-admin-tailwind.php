<?php
/**
 * Plugin Name: Modern Admin with Tailwind CSS
 * Plugin URI: https://example.com
 * Description: Modernisiert das WordPress Admin-Interface mit Tailwind CSS 3 und Shadcn-ähnlichen Komponenten
 * Version: 1.0.0
 * Author: Admin Modernizer
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ModernAdminTailwind {

    private $plugin_url;
    private $plugin_path;

    public function __construct() {
        $this->plugin_url = plugin_dir_url(__FILE__);
        $this->plugin_path = plugin_dir_path(__FILE__);

        add_action('init', array($this, 'init'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'), 999);
        add_action('admin_head', array($this, 'add_admin_styles'), 999);
        add_action('admin_footer', array($this, 'add_admin_scripts'));
        add_action('admin_bar_menu', array($this, 'modify_admin_bar'), 999);
        add_filter('admin_body_class', array($this, 'add_admin_body_class'));
    }

    public function init() {
        // Remove default WordPress admin styles partially
        add_action('admin_enqueue_scripts', array($this, 'dequeue_admin_styles'), 100);
    }

    public function dequeue_admin_styles() {
        // Don't completely remove, but override with higher priority
        wp_dequeue_style('admin-menu');
        wp_dequeue_style('dashboard');
        wp_dequeue_style('common');
    }

    public function add_admin_body_class($classes) {
        return $classes . ' modern-admin-active';
    }

    public function enqueue_admin_assets() {
        // Enqueue Tailwind CSS with high priority
        wp_enqueue_style(
            'modern-admin-tailwind',
            $this->plugin_url . 'assets/css/modern-admin.css',
            array(),
            '1.0.0'
        );

        // Enqueue custom JavaScript
        wp_enqueue_script(
            'modern-admin-js',
            $this->plugin_url . 'assets/js/modern-admin.js',
            array('jquery'),
            '1.0.0',
            true
        );

        // Add localization for JavaScript
        wp_localize_script('modern-admin-js', 'modernAdmin', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('modern_admin_nonce'),
            'pluginUrl' => $this->plugin_url
        ));
    }

    public function add_admin_styles() {
        echo '<style id="modern-admin-inline-styles">';
        echo $this->get_inline_styles();
        echo '</style>';
    }

    public function add_admin_scripts() {
        echo '<script id="modern-admin-inline-scripts">';
        echo $this->get_inline_scripts();
        echo '</script>';
    }

    private function get_inline_styles() {
        return '
        /* COMPLETE ADMIN OVERRIDE */
        body.modern-admin-active {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
            background: rgb(241, 241, 241) !important;
        }

        /* HIDE ORIGINAL ADMIN MENU COMPLETELY */
        body.modern-admin-active #adminmenuback,
        body.modern-admin-active #adminmenuwrap {
            display: none !important;
        }

        /* HIDE UNWANTED MENU TEXT ELEMENTS */
        body.modern-admin-active .awaiting-mod,
        body.modern-admin-active .update-count {
            display: none !important;
        }

        /* ADJUST CONTENT AREA WITH PROPER SPACING */
        body.modern-admin-active #wpcontent {
            margin-left: 0 !important;
            padding-left: 320px !important; /* 280px sidebar + 40px spacing */
            padding-right: 40px !important; /* Right spacing */
            box-sizing: border-box !important;
        }

        body.modern-admin-active #wpfooter {
            margin-left: 0 !important;
            padding-left: 320px !important; /* 280px sidebar + 40px spacing */
            padding-right: 40px !important; /* Right spacing */
            box-sizing: border-box !important;
        }

        body.modern-admin-active #wpbody-content {
            padding-bottom: 65px !important;
            padding-top: 20px !important;
            max-width: 100% !important;
            box-sizing: border-box !important;
        }

        /* FORCE PROPER CONTAINER SIZING */
        body.modern-admin-active .wrap {
            max-width: 100% !important;
            margin-right: 0 !important;
            padding-right: 0 !important;
            box-sizing: border-box !important;
        }

        /* OVERRIDE WORDPRESS ADMIN STYLES COMPLETELY */
        body.modern-admin-active .wrap h1 {
            font-size: 2rem !important;
            font-weight: 600 !important;
            color: rgb(29, 35, 39) !important;
            margin-bottom: 1.5rem !important;
        }

        body.modern-admin-active .button,
        body.modern-admin-active .button-primary,
        body.modern-admin-active .button-secondary {
            background: rgb(56, 88, 233) !important;
            color: white !important;
            border: none !important;
            border-radius: 0.375rem !important;
            padding: 0.5rem 1rem !important;
            font-weight: 500 !important;
            transition: all 0.2s !important;
        }

        body.modern-admin-active .button:hover,
        body.modern-admin-active .button-primary:hover {
            background: rgb(33, 69, 230) !important;
            transform: translateY(-1px) !important;
        }

        body.modern-admin-active .button-secondary {
            background: rgb(44, 51, 56) !important;
        }

        body.modern-admin-active .button-secondary:hover {
            background: rgb(26, 30, 35) !important;
        }

        /* MODERNIZE FORM ELEMENTS */
        body.modern-admin-active input[type="text"],
        body.modern-admin-active input[type="email"],
        body.modern-admin-active input[type="password"],
        body.modern-admin-active textarea,
        body.modern-admin-active select {
            border: 1px solid rgb(220, 220, 222) !important;
            border-radius: 0.375rem !important;
            padding: 0.5rem 0.75rem !important;
            font-size: 0.875rem !important;
            transition: all 0.2s !important;
        }

        body.modern-admin-active input[type="text"]:focus,
        body.modern-admin-active input[type="email"]:focus,
        body.modern-admin-active input[type="password"]:focus,
        body.modern-admin-active textarea:focus,
        body.modern-admin-active select:focus {
            border-color: rgb(56, 88, 233) !important;
            box-shadow: 0 0 0 2px rgba(56, 88, 233, 0.1) !important;
            outline: none !important;
        }

        /* MODERNIZE TABLES */
        body.modern-admin-active .wp-list-table {
            border: 1px solid rgb(220, 220, 222) !important;
            border-radius: 0.5rem !important;
            overflow: hidden !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        }

        body.modern-admin-active .wp-list-table th {
            background: rgb(248, 250, 252) !important;
            color: rgb(71, 85, 105) !important;
            font-weight: 500 !important;
            text-transform: uppercase !important;
            font-size: 0.75rem !important;
            letter-spacing: 0.05em !important;
            padding: 1rem !important;
        }

        body.modern-admin-active .wp-list-table td {
            padding: 1rem !important;
            border-bottom: 1px solid rgb(240, 240, 241) !important;
        }

        body.modern-admin-active .wp-list-table tbody tr:hover {
            background: rgb(248, 250, 252) !important;
        }

        /* MODERNIZE DASHBOARD WIDGETS */
        body.modern-admin-active .postbox {
            border: 1px solid rgb(220, 220, 222) !important;
            border-radius: 0.5rem !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            margin-bottom: 1.5rem !important;
        }

        body.modern-admin-active .postbox .hndle {
            background: white !important;
            border-bottom: 1px solid rgb(240, 240, 241) !important;
            padding: 1rem 1.5rem !important;
            font-weight: 600 !important;
            color: rgb(29, 35, 39) !important;
        }

        body.modern-admin-active .postbox .inside {
            padding: 1.5rem !important;
        }

        /* HIDE SCREEN OPTIONS AND HELP */
        body.modern-admin-active #screen-meta-links {
            display: none !important;
        }

        /* MODERNIZE BULK ACTIONS AND FILTERS */
        body.modern-admin-active .tablenav {
            background: transparent !important;
            padding: 1.5rem 0 !important;
            border: none !important;
            border-radius: 0 !important;
            margin-bottom: 1rem !important;
            box-shadow: none !important;
        }

        body.modern-admin-active .tablenav .alignleft,
        body.modern-admin-active .tablenav .alignright {
            display: flex !important;
            align-items: center !important;
            gap: 0.75rem !important;
        }

        body.modern-admin-active .tablenav select {
            background: white !important;
            border: 1px solid rgb(220, 220, 222) !important;
            border-radius: 0.375rem !important;
            padding: 0.5rem 0.75rem !important;
            font-size: 0.875rem !important;
            min-width: 120px !important;
        }

        body.modern-admin-active .tablenav .button,
        body.modern-admin-active .tablenav input[type="submit"] {
            background: rgb(56, 88, 233) !important;
            color: white !important;
            border: none !important;
            border-radius: 0.375rem !important;
            padding: 0.5rem 1rem !important;
            font-weight: 500 !important;
            font-size: 0.875rem !important;
            cursor: pointer !important;
            transition: all 0.2s !important;
        }

        body.modern-admin-active .tablenav .button:hover,
        body.modern-admin-active .tablenav input[type="submit"]:hover {
            background: rgb(33, 69, 230) !important;
            transform: translateY(-1px) !important;
        }

        body.modern-admin-active .tablenav .button.action {
            background: rgb(44, 51, 56) !important;
        }

        body.modern-admin-active .tablenav .button.action:hover {
            background: rgb(26, 30, 35) !important;
        }

        /* MODERNIZE SEARCH BOX */
        body.modern-admin-active .search-box {
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
        }

        body.modern-admin-active .search-box input[type="search"] {
            border: 1px solid rgb(220, 220, 222) !important;
            border-radius: 0.375rem !important;
            padding: 0.5rem 0.75rem !important;
            font-size: 0.875rem !important;
            min-width: 200px !important;
        }

        body.modern-admin-active .search-box input[type="submit"] {
            background: rgb(56, 88, 233) !important;
            color: white !important;
            border: none !important;
            border-radius: 0.375rem !important;
            padding: 0.5rem 1rem !important;
            font-weight: 500 !important;
            cursor: pointer !important;
        }

        /* MODERNIZE PAGINATION */
        body.modern-admin-active .tablenav-pages {
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
        }

        body.modern-admin-active .tablenav-pages .pagination-links a,
        body.modern-admin-active .tablenav-pages .pagination-links span {
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-width: 2.5rem !important;
            height: 2.5rem !important;
            padding: 0.5rem !important;
            border: 1px solid rgb(220, 220, 222) !important;
            border-radius: 0.375rem !important;
            text-decoration: none !important;
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            transition: all 0.2s !important;
        }

        body.modern-admin-active .tablenav-pages .pagination-links a:hover {
            background: rgb(248, 250, 252) !important;
            border-color: rgb(56, 88, 233) !important;
        }

        body.modern-admin-active .tablenav-pages .pagination-links .current {
            background: rgb(56, 88, 233) !important;
            color: white !important;
            border-color: rgb(56, 88, 233) !important;
        }

        /* MODERNIZE ADD NEW BUTTON */
        body.modern-admin-active .page-title-action {
            background: rgb(56, 88, 233) !important;
            color: white !important;
            border: none !important;
            border-radius: 0.375rem !important;
            padding: 0.5rem 1rem !important;
            font-weight: 500 !important;
            text-decoration: none !important;
            transition: all 0.2s !important;
            margin-left: 1rem !important;
        }

        body.modern-admin-active .page-title-action:hover {
            background: rgb(33, 69, 230) !important;
            transform: translateY(-1px) !important;
            color: white !important;
        }

        /* FORCE PROPER SPACING AND REMOVE UNWANTED CONTAINERS */
        body.modern-admin-active * {
            box-sizing: border-box !important;
        }

        body.modern-admin-active #wpbody {
            padding-right: 0 !important;
            margin-right: 0 !important;
        }

        body.modern-admin-active .wp-list-table-wrap {
            margin-right: 0 !important;
            padding-right: 0 !important;
        }

        /* REMOVE WHITE BACKGROUND FROM NAVIGATION AREA */
        body.modern-admin-active .subsubsub {
            background: transparent !important;
            padding: 0 !important;
            margin: 0 0 1rem 0 !important;
        }

        /* ENSURE TABLES DON'T OVERFLOW */
        body.modern-admin-active .wp-list-table {
            width: 100% !important;
            max-width: 100% !important;
            table-layout: fixed !important;
        }

        /* FIX SEARCH BOX POSITIONING */
        body.modern-admin-active .search-box {
            margin: 0 !important;
            padding: 0 !important;
        }
        ';
    }

    private function get_inline_scripts() {
        return '
        jQuery(document).ready(function($) {
            console.log("Modern Admin Tailwind loaded - FULL OVERRIDE MODE");

            // Create and inject modern admin menu
            createModernAdminMenu();

            // Initialize modern components
            initModernComponents();

            // Add modern classes to existing elements
            modernizeExistingElements();
        });

        function createModernAdminMenu() {
            // This will be implemented in the external JS file
            if (typeof window.ModernAdminInterface !== "undefined") {
                new window.ModernAdminInterface();
            }
        }

        function initModernComponents() {
            // Add modern classes to buttons
            $(".button, .button-primary, .button-secondary").addClass("modern-btn");

            // Add modern classes to form elements
            $("input[type=text], input[type=email], input[type=password], textarea, select").addClass("modern-input");

            // Add modern classes to tables
            $(".wp-list-table").addClass("modern-table");
        }

        function modernizeExistingElements() {
            // Add fade-in animations
            $(".postbox, .wp-list-table").each(function(index) {
                $(this).css({
                    "opacity": "0",
                    "transform": "translateY(20px)"
                }).delay(index * 100).animate({
                    "opacity": "1"
                }, 500).css("transform", "translateY(0)");
            });
        }
        ';
    }

    public function modify_admin_bar($wp_admin_bar) {
        // Modernize admin bar if needed
    }
}

// Initialize the plugin
new ModernAdminTailwind();
