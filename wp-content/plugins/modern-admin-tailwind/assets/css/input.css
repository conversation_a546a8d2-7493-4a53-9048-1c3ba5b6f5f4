@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* WordPress Admin Farben als CSS-Variablen */
    --wp-admin-base: 29 35 39; /* #1d2327 */
    --wp-admin-base-light: 44 51 56; /* #2c3338 */
    --wp-admin-base-dark: 26 30 35; /* #1a1e23 */
    --wp-admin-text: 240 240 241; /* #f0f0f1 */
    --wp-admin-text-muted: 195 196 199; /* #c3c4c7 */
    --wp-admin-text-subtle: 140 143 148; /* #8c8f94 */
    --wp-admin-accent: 56 88 233; /* #3858e9 */
    --wp-admin-accent-hover: 33 69 230; /* #2145e6 */
    --wp-admin-accent-light: 123 144 255; /* #7b90ff */
    --wp-admin-background: 241 241 241; /* #f1f1f1 */
    --wp-admin-background-alt: 255 255 255; /* #ffffff */
    --wp-admin-border: 220 220 222; /* #dcdcde */
    --wp-admin-border-subtle: 240 240 241; /* #f0f0f1 */
    --wp-admin-success: 0 163 42; /* #00a32a */
    --wp-admin-warning: 219 166 23; /* #dba617 */
    --wp-admin-error: 214 54 56; /* #d63638 */
    --wp-admin-info: 114 174 230; /* #72aee6 */

    /* Shadcn-kompatible Variablen mit WordPress-Farben */
    --background: var(--wp-admin-background);
    --foreground: var(--wp-admin-base);
    --card: var(--wp-admin-background-alt);
    --card-foreground: var(--wp-admin-base);
    --popover: var(--wp-admin-background-alt);
    --popover-foreground: var(--wp-admin-base);
    --primary: var(--wp-admin-accent);
    --primary-foreground: var(--wp-admin-text);
    --secondary: var(--wp-admin-base-light);
    --secondary-foreground: var(--wp-admin-text);
    --muted: var(--wp-admin-border-subtle);
    --muted-foreground: var(--wp-admin-text-muted);
    --accent: var(--wp-admin-accent-light);
    --accent-foreground: var(--wp-admin-base);
    --destructive: var(--wp-admin-error);
    --destructive-foreground: var(--wp-admin-text);
    --border: var(--wp-admin-border);
    --input: var(--wp-admin-border);
    --ring: var(--wp-admin-accent);
    --radius: 0.5rem;
  }

  /* WordPress Admin Body Styling */
  body.wp-admin {
    @apply font-sans antialiased;
    background-color: rgb(var(--wp-admin-background));
  }

  /* Basis-Styling für alle Admin-Elemente */
  .wp-admin * {
    @apply border-border;
  }

  .wp-admin h1,
  .wp-admin h2,
  .wp-admin h3,
  .wp-admin h4,
  .wp-admin h5,
  .wp-admin h6 {
    @apply font-semibold tracking-tight;
  }

  .wp-admin h1 {
    @apply text-3xl;
  }

  .wp-admin h2 {
    @apply text-2xl;
  }

  .wp-admin h3 {
    @apply text-xl;
  }

  .wp-admin h4 {
    @apply text-lg;
  }
}

@layer components {
  /* Shadcn-inspirierte Button-Komponenten */
  .btn {
    @apply inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply btn bg-primary text-primary-foreground hover:bg-primary/90;
    @apply h-10 px-4 py-2;
  }

  .btn-secondary {
    @apply btn bg-secondary text-secondary-foreground hover:bg-secondary/80;
    @apply h-10 px-4 py-2;
  }

  .btn-outline {
    @apply btn border border-input bg-background hover:bg-accent hover:text-accent-foreground;
    @apply h-10 px-4 py-2;
  }

  .btn-ghost {
    @apply btn hover:bg-accent hover:text-accent-foreground;
    @apply h-10 px-4 py-2;
  }

  .btn-sm {
    @apply h-9 rounded-md px-3;
  }

  .btn-lg {
    @apply h-11 rounded-md px-8;
  }

  /* Card-Komponenten */
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-wp;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Input-Komponenten */
  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Badge-Komponenten */
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-default {
    @apply badge border-transparent bg-primary text-primary-foreground hover:bg-primary/80;
  }

  .badge-secondary {
    @apply badge border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .badge-outline {
    @apply badge text-foreground;
  }

  /* Modern Admin Menu Styling */
  .modern-admin-menu {
    @apply fixed left-0 top-8 h-full w-64 bg-wp-admin-base border-r border-wp-admin-border-subtle shadow-wp-lg transition-all duration-300 ease-in-out z-50;
  }

  .modern-admin-menu.collapsed {
    @apply w-16;
  }

  .modern-admin-menu-header {
    @apply p-4 border-b border-wp-admin-base-light;
  }

  .modern-admin-menu-item {
    @apply flex items-center px-4 py-3 text-wp-admin-text hover:bg-wp-admin-base-light transition-colors duration-200 cursor-pointer;
  }

  .modern-admin-menu-item.active {
    @apply bg-wp-admin-accent text-wp-admin-text border-r-2 border-wp-admin-accent-light;
  }

  .modern-admin-menu-item:hover {
    @apply bg-wp-admin-base-light;
  }

  .modern-admin-menu-icon {
    @apply w-5 h-5 mr-3 text-wp-admin-text-muted transition-colors duration-200;
  }

  .modern-admin-menu-item:hover .modern-admin-menu-icon {
    color: rgb(var(--wp-admin-text));
  }

  .modern-admin-menu-text {
    @apply text-sm font-medium truncate;
  }

  .modern-admin-menu.collapsed .modern-admin-menu-text {
    @apply hidden;
  }

  .modern-admin-menu.collapsed .modern-admin-menu-icon {
    @apply mr-0;
  }

  /* Modern Admin Content Area */
  .modern-admin-content {
    @apply ml-64 transition-all duration-300 ease-in-out;
  }

  .modern-admin-content.menu-collapsed {
    @apply ml-16;
  }

  /* Modern Dashboard Cards */
  .modern-dashboard-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6;
  }

  .modern-dashboard-card {
    @apply card hover:shadow-wp-lg transition-shadow duration-200;
  }

  .modern-dashboard-stat {
    @apply flex items-center justify-between p-6;
  }

  .modern-dashboard-stat-value {
    @apply text-3xl font-bold text-wp-admin-base;
  }

  .modern-dashboard-stat-label {
    @apply text-sm text-wp-admin-text-muted;
  }

  .modern-dashboard-stat-icon {
    @apply w-8 h-8 text-wp-admin-accent;
  }

  /* Modern Table Styling */
  .modern-table {
    @apply w-full border-collapse bg-card rounded-lg overflow-hidden shadow-wp;
  }

  .modern-table th {
    @apply bg-muted/50 px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider border-b border-border;
  }

  .modern-table td {
    @apply px-4 py-3 text-sm text-foreground border-b border-border;
  }

  .modern-table tbody tr:hover {
    @apply bg-muted/30;
  }

  .modern-table tbody tr:last-child td {
    @apply border-b-0;
  }

  /* Modern Form Styling */
  .modern-form-group {
    @apply space-y-2;
  }

  .modern-form-label {
    @apply text-sm font-medium text-foreground;
  }

  .modern-form-description {
    @apply text-xs text-muted-foreground;
  }

  /* Modern Notification/Alert Styling */
  .modern-alert {
    @apply p-4 rounded-lg border;
  }

  .modern-alert-success {
    @apply modern-alert bg-green-50 border-green-200 text-green-800;
  }

  .modern-alert-warning {
    @apply modern-alert bg-yellow-50 border-yellow-200 text-yellow-800;
  }

  .modern-alert-error {
    @apply modern-alert bg-red-50 border-red-200 text-red-800;
  }

  .modern-alert-info {
    @apply modern-alert bg-blue-50 border-blue-200 text-blue-800;
  }

  /* Modern Tabs */
  .modern-tabs {
    @apply border-b border-border;
  }

  .modern-tab {
    @apply inline-flex items-center px-4 py-2 text-sm font-medium text-muted-foreground border-b-2 border-transparent hover:text-foreground hover:border-border transition-colors duration-200;
  }

  .modern-tab.active {
    @apply text-primary border-primary;
  }

  /* Modern Modal/Dialog */
  .modern-modal-overlay {
    @apply fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4;
  }

  .modern-modal {
    @apply bg-card rounded-lg shadow-wp-xl max-w-md w-full max-h-[90vh] overflow-y-auto;
  }

  .modern-modal-header {
    @apply px-6 py-4 border-b border-border;
  }

  .modern-modal-title {
    @apply text-lg font-semibold text-foreground;
  }

  .modern-modal-content {
    @apply px-6 py-4;
  }

  .modern-modal-footer {
    @apply px-6 py-4 border-t border-border flex justify-end space-x-2;
  }

  /* WordPress Admin Bar Modernization */
  #wpadminbar {
    @apply bg-wp-admin-base border-b border-wp-admin-border-subtle shadow-wp;
  }

  #wpadminbar .ab-top-menu > li > .ab-item {
    @apply text-wp-admin-text hover:bg-wp-admin-base-light transition-colors duration-200;
  }

  /* Modern Search Box */
  .modern-search-box {
    @apply relative;
  }

  .modern-search-input {
    @apply input pl-10;
  }

  .modern-search-icon {
    @apply absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground;
  }

  /* Modern Pagination */
  .modern-pagination {
    @apply flex items-center space-x-1;
  }

  .modern-pagination-item {
    @apply px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted rounded-md transition-colors duration-200;
  }

  .modern-pagination-item.active {
    @apply bg-primary text-primary-foreground;
  }

  .modern-pagination-item.disabled {
    @apply opacity-50 cursor-not-allowed;
  }
}
