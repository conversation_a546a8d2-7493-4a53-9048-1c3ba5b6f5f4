/**
 * Modern Admin JavaScript - COMPLETE WORDPRESS ADMIN OVERRIDE
 * Ersetzt das komplette WordPress Admin-Interface
 */

(function($) {
    'use strict';

    // Make it globally available
    window.ModernAdminInterface = class ModernAdminInterface {
        constructor() {
            this.isMenuCollapsed = false;
            this.originalMenuItems = [];
            this.init();
        }

        init() {
            console.log('Initializing Modern Admin Interface - FULL OVERRIDE');

            // Wait for DOM to be fully loaded
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    this.startModernization();
                });
            } else {
                this.startModernization();
            }
        }

        startModernization() {
            this.extractOriginalMenuData();
            this.createModernMenu();
            this.enhanceExistingElements();
            this.bindEvents();
            this.initAnimations();
            this.addModernNotifications();
        }

        extractOriginalMenuData() {
            // Extract menu data before hiding original menu
            this.originalMenuItems = [];
            $('#adminmenu > li').each((index, element) => {
                const $item = $(element);
                const $link = $item.find('a').first();

                // Get clean title - only from .wp-menu-name, ignore other text
                let title = $link.find('.wp-menu-name').text().trim();

                // If no .wp-menu-name found, get text but clean it
                if (!title) {
                    title = $link.clone().children().remove().end().text().trim();
                }

                // Clean up title - remove any moderation text or extra content
                title = this.cleanMenuTitle(title);

                const url = $link.attr('href');
                const classes = $item.attr('class') || '';
                const slug = classes.match(/menu-icon-(\w+)/)?.[1] || 'generic';
                const current = $item.hasClass('current') || $item.hasClass('wp-has-current-submenu');

                // Get count from update-count or awaiting-mod, but only numbers
                let count = '';
                const $countElement = $item.find('.update-count, .awaiting-mod');
                if ($countElement.length > 0) {
                    const countText = $countElement.text().trim();
                    const countMatch = countText.match(/\d+/);
                    if (countMatch && parseInt(countMatch[0]) > 0) {
                        count = countMatch[0];
                    }
                }

                if (title && url && !$item.hasClass('wp-menu-separator')) {
                    this.originalMenuItems.push({
                        title,
                        url,
                        slug,
                        current,
                        count,
                        hasSubmenu: $item.find('.wp-submenu').length > 0,
                        submenuItems: this.extractSubmenuItems($item)
                    });
                }
            });

            console.log('Extracted menu items:', this.originalMenuItems);
        }

        cleanMenuTitle(title) {
            // Remove common unwanted text patterns from menu titles
            const cleanPatterns = [
                /\d+\s*(Comments?|Kommentare?)\s*in\s*(moderation|Moderation)/gi,
                /\(\d+\)/g, // Remove parentheses with numbers
                /\d+\s*awaiting\s*moderation/gi,
                /\d+\s*pending/gi,
                /\s*-\s*\d+.*$/gi, // Remove trailing dash with numbers
                /^\s*\d+\s*/, // Remove leading numbers
                /\s{2,}/g // Replace multiple spaces with single space
            ];

            let cleanTitle = title;
            cleanPatterns.forEach(pattern => {
                cleanTitle = cleanTitle.replace(pattern, '');
            });

            return cleanTitle.trim();
        }

        extractSubmenuItems($parentItem) {
            const submenuItems = [];
            $parentItem.find('.wp-submenu li a').each((index, element) => {
                const $link = $(element);
                const title = $link.text().trim();
                const url = $link.attr('href');
                if (title && url) {
                    submenuItems.push({ title, url });
                }
            });
            return submenuItems;
        }

        createModernMenu() {
            console.log('Creating modern menu...');

            // Create modern menu structure
            const modernMenu = this.buildModernMenu();

            // Insert modern menu into page
            $('body').append(modernMenu);

            console.log('Modern menu created and inserted');
        }

        buildModernMenu() {
            let menuHTML = `
                <div class="modern-admin-menu" id="modern-admin-menu" style="
                    position: fixed;
                    left: 0;
                    top: 32px;
                    bottom: 0;
                    width: 280px;
                    background: rgb(29, 35, 39);
                    border-right: 1px solid rgb(220, 220, 222);
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    z-index: 9999;
                    display: flex;
                    flex-direction: column;
                    transition: all 0.3s ease;
                ">
                    <div class="modern-admin-menu-header" style="
                        padding: 1rem;
                        border-bottom: 1px solid rgb(44, 51, 56);
                    ">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <h2 style="
                                font-size: 1.125rem;
                                font-weight: 600;
                                color: rgb(240, 240, 241);
                                margin: 0;
                            ">WordPress</h2>
                            <button id="menu-toggle" style="
                                background: transparent;
                                border: none;
                                color: rgb(240, 240, 241);
                                padding: 0.5rem;
                                border-radius: 0.375rem;
                                cursor: pointer;
                                transition: background-color 0.2s;
                            " onmouseover="this.style.backgroundColor='rgb(44, 51, 56)'" onmouseout="this.style.backgroundColor='transparent'">
                                <svg style="width: 1rem; height: 1rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <nav style="flex: 1; overflow-y: auto; padding: 0.5rem 0;">
            `;

            this.originalMenuItems.forEach(item => {
                const isActive = item.current ? 'active' : '';
                const icon = this.getMenuIcon(item.slug);
                const activeStyles = item.current ? `
                    background: rgb(56, 88, 233) !important;
                    border-right: 2px solid rgb(123, 144, 255);
                ` : '';

                menuHTML += `
                    <a href="${item.url}" class="modern-admin-menu-item ${isActive}" data-slug="${item.slug}" style="
                        display: flex;
                        align-items: center;
                        padding: 0.75rem 1rem;
                        color: rgb(240, 240, 241);
                        text-decoration: none;
                        transition: all 0.2s;
                        cursor: pointer;
                        ${activeStyles}
                    " onmouseover="if(!this.classList.contains('active')) this.style.backgroundColor='rgb(44, 51, 56)'"
                       onmouseout="if(!this.classList.contains('active')) this.style.backgroundColor='transparent'">
                        <div class="modern-admin-menu-icon" style="
                            width: 1.25rem;
                            height: 1.25rem;
                            margin-right: 0.75rem;
                            color: rgb(195, 196, 199);
                            transition: color 0.2s;
                        ">${icon}</div>
                        <span class="modern-admin-menu-text" style="
                            font-size: 0.875rem;
                            font-weight: 500;
                            flex: 1;
                        ">${item.title}</span>
                        ${item.count ? `<span style="
                            background: rgb(44, 51, 56);
                            color: rgb(240, 240, 241);
                            padding: 0.125rem 0.5rem;
                            border-radius: 9999px;
                            font-size: 0.75rem;
                            font-weight: 600;
                            margin-left: auto;
                        ">${item.count}</span>` : ''}
                    </a>
                `;
            });

            menuHTML += `
                    </nav>
                </div>
            `;

            return menuHTML;
        }

        enhanceExistingElements() {
            console.log('Enhancing existing elements...');

            // Add modern classes and styles to existing WordPress elements
            this.modernizeButtons();
            this.modernizeFormElements();
            this.modernizeTables();
            this.modernizeDashboardWidgets();
            this.modernizeNotices();
        }

        modernizeButtons() {
            $('.button, .button-primary, .button-secondary, input[type="submit"]').each(function() {
                const $btn = $(this);
                $btn.css({
                    'background': 'rgb(56, 88, 233)',
                    'color': 'white',
                    'border': 'none',
                    'border-radius': '0.375rem',
                    'padding': '0.5rem 1rem',
                    'font-weight': '500',
                    'transition': 'all 0.2s',
                    'cursor': 'pointer'
                });

                $btn.hover(
                    function() { $(this).css('background', 'rgb(33, 69, 230)'); },
                    function() { $(this).css('background', 'rgb(56, 88, 233)'); }
                );
            });

            $('.button-secondary').css('background', 'rgb(44, 51, 56)').hover(
                function() { $(this).css('background', 'rgb(26, 30, 35)'); },
                function() { $(this).css('background', 'rgb(44, 51, 56)'); }
            );
        }

        modernizeFormElements() {
            $('input[type="text"], input[type="email"], input[type="password"], input[type="url"], input[type="number"], textarea, select').each(function() {
                $(this).css({
                    'border': '1px solid rgb(220, 220, 222)',
                    'border-radius': '0.375rem',
                    'padding': '0.5rem 0.75rem',
                    'font-size': '0.875rem',
                    'transition': 'all 0.2s'
                });

                $(this).focus(function() {
                    $(this).css({
                        'border-color': 'rgb(56, 88, 233)',
                        'box-shadow': '0 0 0 2px rgba(56, 88, 233, 0.1)',
                        'outline': 'none'
                    });
                }).blur(function() {
                    $(this).css({
                        'border-color': 'rgb(220, 220, 222)',
                        'box-shadow': 'none'
                    });
                });
            });
        }

        modernizeTables() {
            $('.wp-list-table').each(function() {
                $(this).css({
                    'border': '1px solid rgb(220, 220, 222)',
                    'border-radius': '0.5rem',
                    'overflow': 'hidden',
                    'box-shadow': '0 1px 3px rgba(0, 0, 0, 0.1)'
                });

                $(this).find('th').css({
                    'background': 'rgb(248, 250, 252)',
                    'color': 'rgb(71, 85, 105)',
                    'font-weight': '500',
                    'text-transform': 'uppercase',
                    'font-size': '0.75rem',
                    'letter-spacing': '0.05em',
                    'padding': '1rem'
                });

                $(this).find('td').css({
                    'padding': '1rem',
                    'border-bottom': '1px solid rgb(240, 240, 241)'
                });

                $(this).find('tbody tr').hover(
                    function() { $(this).css('background', 'rgb(248, 250, 252)'); },
                    function() { $(this).css('background', 'white'); }
                );
            });
        }

        modernizeDashboardWidgets() {
            $('.postbox').each(function() {
                $(this).css({
                    'border': '1px solid rgb(220, 220, 222)',
                    'border-radius': '0.5rem',
                    'box-shadow': '0 1px 3px rgba(0, 0, 0, 0.1)',
                    'margin-bottom': '1.5rem',
                    'background': 'white'
                });

                $(this).find('.hndle, .postbox-header').css({
                    'background': 'white',
                    'border-bottom': '1px solid rgb(240, 240, 241)',
                    'padding': '1rem 1.5rem',
                    'font-weight': '600',
                    'color': 'rgb(29, 35, 39)'
                });

                $(this).find('.inside').css({
                    'padding': '1.5rem'
                });
            });
        }

        modernizeNotices() {
            $('.notice, .updated, .error').each(function() {
                const $notice = $(this);
                $notice.css({
                    'border-radius': '0.5rem',
                    'padding': '1rem',
                    'margin': '1rem 0',
                    'border-left': '4px solid'
                });

                if ($notice.hasClass('notice-success') || $notice.hasClass('updated')) {
                    $notice.css({
                        'background': 'rgb(240, 253, 244)',
                        'border-left-color': 'rgb(34, 197, 94)',
                        'color': 'rgb(21, 128, 61)'
                    });
                } else if ($notice.hasClass('notice-error') || $notice.hasClass('error')) {
                    $notice.css({
                        'background': 'rgb(254, 242, 242)',
                        'border-left-color': 'rgb(239, 68, 68)',
                        'color': 'rgb(153, 27, 27)'
                    });
                } else if ($notice.hasClass('notice-warning')) {
                    $notice.css({
                        'background': 'rgb(255, 251, 235)',
                        'border-left-color': 'rgb(245, 158, 11)',
                        'color': 'rgb(146, 64, 14)'
                    });
                } else {
                    $notice.css({
                        'background': 'rgb(239, 246, 255)',
                        'border-left-color': 'rgb(59, 130, 246)',
                        'color': 'rgb(30, 64, 175)'
                    });
                }
            });
        }

        getMenuIcon(slug) {
            const icons = {
                'dashboard': '<svg style="width: 100%; height: 100%;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path></svg>',
                'posts': '<svg style="width: 100%; height: 100%;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>',
                'media': '<svg style="width: 100%; height: 100%;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>',
                'pages': '<svg style="width: 100%; height: 100%;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>',
                'comments': '<svg style="width: 100%; height: 100%;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>',
                'appearance': '<svg style="width: 100%; height: 100%;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path></svg>',
                'plugins': '<svg style="width: 100%; height: 100%;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path></svg>',
                'users': '<svg style="width: 100%; height: 100%;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg>',
                'tools': '<svg style="width: 100%; height: 100%;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>',
                'settings': '<svg style="width: 100%; height: 100%;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>'
            };

            return icons[slug] || '<svg style="width: 100%; height: 100%;" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>';
        }

        bindEvents() {
            console.log('Binding events...');

            // Menu Toggle
            $(document).on('click', '#menu-toggle', () => {
                this.toggleMenu();
            });

            // Menu Item Hover Effects
            $(document).on('mouseenter', '.modern-admin-menu-item', function() {
                if (!$(this).hasClass('active')) {
                    $(this).find('.modern-admin-menu-icon').css('color', 'rgb(240, 240, 241)');
                }
            });

            $(document).on('mouseleave', '.modern-admin-menu-item', function() {
                if (!$(this).hasClass('active')) {
                    $(this).find('.modern-admin-menu-icon').css('color', 'rgb(195, 196, 199)');
                }
            });
        }

        toggleMenu() {
            this.isMenuCollapsed = !this.isMenuCollapsed;
            const $menu = $('#modern-admin-menu');

            if (this.isMenuCollapsed) {
                $menu.css('width', '80px');
                $menu.find('.modern-admin-menu-text').hide();
                $menu.find('.modern-admin-menu-icon').css('margin-right', '0');
                $('#wpcontent, #wpfooter').css('padding-left', '100px');
            } else {
                $menu.css('width', '280px');
                $menu.find('.modern-admin-menu-text').show();
                $menu.find('.modern-admin-menu-icon').css('margin-right', '0.75rem');
                $('#wpcontent, #wpfooter').css('padding-left', '280px');
            }
        }

        initAnimations() {
            console.log('Initializing animations...');

            // Fade-in Animation für das Menü
            $('#modern-admin-menu').css({
                'opacity': '0',
                'transform': 'translateX(-100%)'
            }).animate({
                'opacity': '1'
            }, 500).css('transform', 'translateX(0)');

            // Staggered animation für Menü-Items
            $('.modern-admin-menu-item').each(function(index) {
                $(this).css({
                    'opacity': '0',
                    'transform': 'translateX(-20px)'
                }).delay(index * 50).animate({
                    'opacity': '1'
                }, 300).css('transform', 'translateX(0)');
            });
        }

        addModernNotifications() {
            // Add a modern notification that the interface has been modernized
            const notification = `
                <div id="modern-admin-notification" style="
                    position: fixed;
                    top: 50px;
                    right: 20px;
                    background: rgb(34, 197, 94);
                    color: white;
                    padding: 1rem 1.5rem;
                    border-radius: 0.5rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    z-index: 10000;
                    font-weight: 500;
                    opacity: 0;
                    transform: translateY(-20px);
                    transition: all 0.3s ease;
                ">
                    ✨ WordPress Admin modernisiert mit Tailwind CSS!
                </div>
            `;

            $('body').append(notification);

            setTimeout(() => {
                $('#modern-admin-notification').css({
                    'opacity': '1',
                    'transform': 'translateY(0)'
                });
            }, 1000);

            setTimeout(() => {
                $('#modern-admin-notification').css({
                    'opacity': '0',
                    'transform': 'translateY(-20px)'
                });
                setTimeout(() => {
                    $('#modern-admin-notification').remove();
                }, 300);
            }, 5000);
        }
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        console.log('DOM ready, initializing Modern Admin Interface...');
        new window.ModernAdminInterface();
    });

})(jQuery);
