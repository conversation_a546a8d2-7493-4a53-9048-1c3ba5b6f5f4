/**
 * Modern Admin JavaScript
 * Modernisiert das WordPress Admin-Interface mit interaktiven Komponenten
 */

(function($) {
    'use strict';

    class ModernAdminInterface {
        constructor() {
            this.isMenuCollapsed = false;
            this.init();
        }

        init() {
            this.createModernMenu();
            this.createModernDashboard();
            this.enhanceExistingElements();
            this.bindEvents();
            this.initAnimations();
        }

        createModernMenu() {
            // Verstecke das ursprüngliche Admin-Menü
            $('#adminmenu').hide();
            
            // Erstelle das moderne Menü
            const modernMenu = this.buildModernMenu();
            $('body').append(modernMenu);
            
            // Passe den Content-Bereich an
            $('#wpcontent').addClass('modern-admin-content');
        }

        buildModernMenu() {
            const menuItems = this.extractMenuItems();
            
            let menuHTML = `
                <div class="modern-admin-menu" id="modern-admin-menu">
                    <div class="modern-admin-menu-header">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-semibold text-wp-admin-text">WordPress</h2>
                            <button id="menu-toggle" class="btn-ghost btn-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <nav class="flex-1 overflow-y-auto">
            `;

            menuItems.forEach(item => {
                const isActive = item.current ? 'active' : '';
                const icon = this.getMenuIcon(item.slug);
                
                menuHTML += `
                    <a href="${item.url}" class="modern-admin-menu-item ${isActive}" data-slug="${item.slug}">
                        <div class="modern-admin-menu-icon">${icon}</div>
                        <span class="modern-admin-menu-text">${item.title}</span>
                        ${item.count ? `<span class="badge badge-secondary ml-auto">${item.count}</span>` : ''}
                    </a>
                `;
            });

            menuHTML += `
                    </nav>
                </div>
            `;

            return menuHTML;
        }

        extractMenuItems() {
            const items = [];
            
            // Extrahiere Menü-Items aus dem ursprünglichen WordPress-Menü
            $('#adminmenu > li').each(function() {
                const $item = $(this);
                const $link = $item.find('a').first();
                const title = $link.find('.wp-menu-name').text().trim();
                const url = $link.attr('href');
                const slug = $item.attr('class').match(/menu-icon-(\w+)/)?.[1] || 'generic';
                const current = $item.hasClass('current');
                const count = $item.find('.update-count').text().trim();

                if (title && url) {
                    items.push({ title, url, slug, current, count });
                }
            });

            return items;
        }

        getMenuIcon(slug) {
            const icons = {
                'dashboard': '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path></svg>',
                'posts': '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>',
                'media': '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>',
                'pages': '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>',
                'comments': '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>',
                'appearance': '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path></svg>',
                'plugins': '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path></svg>',
                'users': '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg>',
                'tools': '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>',
                'settings': '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>'
            };

            return icons[slug] || icons['generic'] || '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>';
        }

        createModernDashboard() {
            // Modernisiere das Dashboard nur auf der Dashboard-Seite
            if (window.location.href.includes('wp-admin/index.php') || window.location.href.endsWith('wp-admin/')) {
                this.enhanceDashboard();
            }
        }

        enhanceDashboard() {
            // Verstecke ursprüngliche Dashboard-Widgets temporär
            $('#dashboard-widgets').hide();
            
            // Erstelle moderne Dashboard-Karten
            const modernDashboard = this.buildModernDashboard();
            $('#wpbody-content').prepend(modernDashboard);
        }

        buildModernDashboard() {
            return `
                <div class="modern-dashboard-grid">
                    <div class="modern-dashboard-card">
                        <div class="modern-dashboard-stat">
                            <div>
                                <div class="modern-dashboard-stat-value">150</div>
                                <div class="modern-dashboard-stat-label">Beiträge</div>
                            </div>
                            <div class="modern-dashboard-stat-icon">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modern-dashboard-card">
                        <div class="modern-dashboard-stat">
                            <div>
                                <div class="modern-dashboard-stat-value">25</div>
                                <div class="modern-dashboard-stat-label">Seiten</div>
                            </div>
                            <div class="modern-dashboard-stat-icon">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modern-dashboard-card">
                        <div class="modern-dashboard-stat">
                            <div>
                                <div class="modern-dashboard-stat-value">8</div>
                                <div class="modern-dashboard-stat-label">Kommentare</div>
                            </div>
                            <div class="modern-dashboard-stat-icon">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modern-dashboard-card">
                        <div class="modern-dashboard-stat">
                            <div>
                                <div class="modern-dashboard-stat-value">3</div>
                                <div class="modern-dashboard-stat-label">Benutzer</div>
                            </div>
                            <div class="modern-dashboard-stat-icon">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        enhanceExistingElements() {
            // Modernisiere Buttons
            $('.button, .button-primary, .button-secondary').each(function() {
                const $btn = $(this);
                if ($btn.hasClass('button-primary')) {
                    $btn.addClass('btn-primary');
                } else {
                    $btn.addClass('btn-secondary');
                }
            });

            // Modernisiere Input-Felder
            $('input[type="text"], input[type="email"], input[type="password"], textarea, select').addClass('input');

            // Modernisiere Tabellen
            $('.wp-list-table').addClass('modern-table');
        }

        bindEvents() {
            // Menu Toggle
            $(document).on('click', '#menu-toggle', () => {
                this.toggleMenu();
            });

            // Menu Item Hover Effects
            $(document).on('mouseenter', '.modern-admin-menu-item', function() {
                $(this).addClass('animate-slide-up');
            });

            $(document).on('mouseleave', '.modern-admin-menu-item', function() {
                $(this).removeClass('animate-slide-up');
            });
        }

        toggleMenu() {
            this.isMenuCollapsed = !this.isMenuCollapsed;
            const $menu = $('#modern-admin-menu');
            const $content = $('#wpcontent');

            if (this.isMenuCollapsed) {
                $menu.addClass('collapsed');
                $content.addClass('menu-collapsed');
            } else {
                $menu.removeClass('collapsed');
                $content.removeClass('menu-collapsed');
            }
        }

        initAnimations() {
            // Fade-in Animation für alle Karten
            $('.modern-dashboard-card').each(function(index) {
                $(this).css('animation-delay', `${index * 0.1}s`).addClass('animate-fade-in');
            });

            // Slide-in Animation für das Menü
            $('#modern-admin-menu').addClass('animate-slide-in');
        }
    }

    // Initialize when DOM is ready
    $(document).ready(function() {
        new ModernAdminInterface();
    });

})(jQuery);
