{"name": "modern-admin-tailwind", "version": "1.0.0", "description": "Modern WordPress Admin with Tailwind CSS 3", "main": "index.js", "scripts": {"build-css": "tailwindcss -i ./assets/css/input.css -o ./assets/css/modern-admin.css --watch", "build-css-prod": "tailwindcss -i ./assets/css/input.css -o ./assets/css/modern-admin.css --minify", "dev": "npm run build-css", "build": "npm run build-css-prod"}, "keywords": ["wordpress", "admin", "tailwind", "modern", "ui"], "author": "Admin Modernizer", "license": "GPL-2.0-or-later", "devDependencies": {"tailwindcss": "^3.4.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10"}}